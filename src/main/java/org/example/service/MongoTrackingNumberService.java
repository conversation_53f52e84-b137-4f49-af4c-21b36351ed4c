package org.example.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import org.example.exception.TrackingNumberGenerationException;
import org.example.model.TrackingNumberRecord;
import org.example.repository.TrackingNumberRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
public class MongoTrackingNumberService implements TrackingNumberService {
    private static final Logger logger = LoggerFactory.getLogger(MongoTrackingNumberService.class);

    private final TrackingNumberRepository trackingNumberRepository;
    private final MongoOperations mongoOperations;
    private final AtomicLong counter = new AtomicLong(0);

    @Autowired
    private Counter trackingNumberGeneratedCounter;

    @Autowired
    private Timer trackingNumberGenerationTimer;

    @Autowired
    private Counter trackingNumberErrorCounter;

    public record CounterDocument(String id, long sequence) {}

    public MongoTrackingNumberService(TrackingNumberRepository trackingNumberRepository,
                                     MongoOperations mongoOperations) {
        this.trackingNumberRepository = trackingNumberRepository;
        this.mongoOperations = mongoOperations;
    }
    
    @Override
    public String generateTrackingNumber(String originCountryId, String destinationCountryId,
                                        double weight, String customerId) {
        try {
            return trackingNumberGenerationTimer.recordCallable(() -> {
            logger.info("Generating tracking number for route: {} -> {}, weight: {}, customer: {}",
                       originCountryId, destinationCountryId, weight, customerId);

            try {
            // Generate a unique timestamp
            long timestamp = System.currentTimeMillis();
            
            // Get daily counter document for uniqueness within a day
            Query query = new Query(Criteria.where("id").is("daily_counter_" + 
                Instant.ofEpochMilli(timestamp).atZone(java.time.ZoneOffset.UTC).toLocalDate()));
            Update update = new Update().inc("sequence", 1);
            FindAndModifyOptions options = new FindAndModifyOptions().returnNew(true).upsert(true);
            
            CounterDocument counterDoc = mongoOperations.findAndModify(query, update, options, CounterDocument.class);
            long dailySequence = counterDoc != null ? counterDoc.sequence() : new Random().nextLong(100000);
            
            // Create input string for hashing
            String input = originCountryId + destinationCountryId + 
                          String.format("%.3f", weight) + customerId + 
                          timestamp + dailySequence;
            
            // Generate SHA-256 hash
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes());
            
            // Convert to hexadecimal string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            // Take first 16 characters and convert to uppercase
            String rawTrackingNumber = hexString.toString().substring(0, 16).toUpperCase();
            
            // Save tracking number record to MongoDB
            TrackingNumberRecord record = new TrackingNumberRecord(
                rawTrackingNumber, originCountryId, destinationCountryId, weight, customerId);
            trackingNumberRepository.save(record);

            trackingNumberGeneratedCounter.increment();
            logger.info("Successfully generated tracking number: {}", rawTrackingNumber);

            return rawTrackingNumber;
            
            } catch (NoSuchAlgorithmException e) {
                trackingNumberErrorCounter.increment();
                logger.warn("SHA-256 not available, using fallback method", e);

                // Fallback to simpler approach if SHA-256 is not available
                String combined = originCountryId + destinationCountryId +
                                String.format("%.3f", weight) + "-" +
                                customerId.substring(0, Math.min(8, customerId.length())) +
                                System.currentTimeMillis() + counter.incrementAndGet();

                String trackingNumber = combined.replaceAll("[^A-Z0-9]", "").toUpperCase().substring(0, Math.min(16, combined.length()));

                // Save tracking number record to MongoDB
                TrackingNumberRecord record = new TrackingNumberRecord(
                    trackingNumber, originCountryId, destinationCountryId, weight, customerId);
                trackingNumberRepository.save(record);

                trackingNumberGeneratedCounter.increment();
                logger.info("Generated tracking number using fallback method: {}", trackingNumber);

                return trackingNumber;
            } catch (Exception e) {
                trackingNumberErrorCounter.increment();
                logger.error("Error generating tracking number", e);
                throw new TrackingNumberGenerationException("Failed to generate tracking number", e);
            }
        });
    }
    
    // Get tracking numbers by customer ID using Java Streams
    public List<TrackingNumberRecord> getTrackingNumbersByCustomer(String customerId) {
        return trackingNumberRepository.findByCustomerId(customerId)
            .stream()
            .filter(record -> record.getCreatedAt().isAfter(Instant.now().minusSeconds(86400)))
            .collect(Collectors.toList());
    }
    
    // Get tracking numbers by route
    public List<TrackingNumberRecord> getTrackingNumbersByRoute(String originCountryId, String destinationCountryId) {
        return trackingNumberRepository.findByOriginCountryIdAndDestinationCountryId(originCountryId, destinationCountryId);
    }
}