package org.example;

import org.example.service.MongoTrackingNumberService;
import org.example.service.TrackingNumberService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@SpringBootApplication
public class TrackingNumberApplication {
    public static void main(String[] args) {
        SpringApplication.run(TrackingNumberApplication.class, args);
    }
    
    @Bean
    public TrackingNumberService trackingNumberService(
            MongoTrackingNumberService mongoTrackingNumberService) {
        return mongoTrackingNumberService;
    }
}