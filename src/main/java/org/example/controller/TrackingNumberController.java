package org.example.controller;

import org.example.model.TrackingNumberRecord;
import org.example.service.TrackingNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.time.Instant;
import java.util.*;

@RestController
@RequestMapping("/api/v1")
public class TrackingNumberController {
    @Autowired
    private TrackingNumberService trackingNumberService;
    
    // Sealed interface for request validation - Modern Java feature
    public sealed interface ValidTrackingRequest permits TrackingNumberRequest {}
    
    @GetMapping("/next-tracking-number")
    public ResponseEntity<Map<String, Object>> getNextTrackingNumber(
            @Valid TrackingNumberRequest request) {
        return generateResponse(request.origin_country_id(), request.destination_country_id(),
                               request.weight(), request.customer_id());
    }
    
    // New endpoint to get tracking numbers by customer
    @GetMapping("/tracking-numbers/customer/{customerId}")
    public List<Map<String, Object>> getTrackingNumbersByCustomer(@PathVariable String customerId) {
        return trackingNumberService.getTrackingNumbersByCustomer(customerId)
            .stream()
            .map(this::createTrackingNumberResponse)
            .toList();
    }
    
    // New endpoint to get tracking numbers by route
    @GetMapping("/tracking-numbers/route/{origin}/{destination}")
    public List<Map<String, Object>> getTrackingNumbersByRoute(@PathVariable String origin, @PathVariable String destination) {
        return trackingNumberService.getTrackingNumbersByRoute(origin, destination)
            .stream()
            .map(this::createTrackingNumberResponse)
            .toList();
    }
    
    private ResponseEntity<Map<String, Object>> generateResponse(String originCountryId, String destinationCountryId,
                                                                 double weight, String customerId) {
        String trackingNumber = trackingNumberService.generateTrackingNumber(
            originCountryId, destinationCountryId, weight, customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("tracking_number", trackingNumber);
        response.put("created_at", Instant.now().toString());
        response.put("origin_country_id", originCountryId);
        response.put("destination_country_id", destinationCountryId);
        
        return ResponseEntity.ok(response);
    }
    
    private Map<String, Object> createTrackingNumberResponse(TrackingNumberRecord record) {
        Map<String, Object> response = new HashMap<>();
        response.put("tracking_number", record.getTrackingNumber());
        response.put("created_at", record.getCreatedAt().toString());
        response.put("recorded_at", record.getRecordedAt().toString());
        response.put("origin_country_id", record.getOriginCountryId());
        response.put("destination_country_id", record.getDestinationCountryId());
        response.put("weight", record.getWeight());
        response.put("customer_id", record.getCustomerId());
        return response;
    }
    
    // Record for immutable data transfer - Modern Java feature
    public record TrackingNumberRequest(
            @NotNull(message = "Origin country ID is required")
            @Pattern(regexp = "[A-Z]{2}", message = "Origin country code must be in ISO 3166-1 alpha-2 format")
            String origin_country_id,

            @NotNull(message = "Destination country ID is required")
            @Pattern(regexp = "[A-Z]{2}", message = "Destination country code must be in ISO 3166-1 alpha-2 format")
            String destination_country_id,

            @Positive(message = "Weight must be positive")
            double weight,

            @NotNull(message = "Customer ID is required")
            @Pattern(regexp = "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}",
                     message = "Customer ID must be a valid UUID")
            String customer_id,

            @NotNull(message = "Customer name is required")
            String customer_name,

            @NotNull(message = "Customer slug is required")
            @Pattern(regexp = "^[a-z0-9]+(-[a-z0-9]+)*$",
                     message = "Customer slug must be in kebab-case format")
            String customer_slug) implements ValidTrackingRequest {}
    
    // Nested record for route request - Modern Java feature
    public record RouteRequest(String origin, String destination) {}
}