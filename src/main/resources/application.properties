# Application configuration
server.port=8080

# Application name
spring.application.name=tracking-number-generator

# MongoDB configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=trackingdb

# Logging configuration
logging.level.org.example=INFO

# Actuator configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always