package org.example.controller;

import org.example.model.TrackingNumberRecord;
import org.example.service.TrackingNumberService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = TrackingNumberController.class,
           excludeAutoConfiguration = {
               org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class,
               org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class
           })
class TrackingNumberControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TrackingNumberService trackingNumberService;

    @Test
    @WithMockUser
    void getNextTrackingNumber_ValidRequest_ShouldReturnTrackingNumber() throws Exception {
        // Arrange
        when(trackingNumberService.generateTrackingNumber(anyString(), anyString(), anyDouble(), anyString()))
            .thenReturn("ABC123DEF456GH78");

        // Act & Assert
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.tracking_number").value("ABC123DEF456GH78"))
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.origin_country_id").value("MY"))
                .andExpect(jsonPath("$.destination_country_id").value("ID"));
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidCountryCode_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "INVALID")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidWeight_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "-1.0")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidUUID_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "invalid-uuid")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidCustomerSlug_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "Invalid_Slug"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getNextTrackingNumber_Unauthenticated_ShouldReturnUnauthorized() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser
    void getTrackingNumbersByCustomer_ShouldReturnList() throws Exception {
        // Arrange
        String customerId = "de619854-b59b-425e-9db4-943979e1bd49";
        List<TrackingNumberRecord> mockRecords = Arrays.asList(
            new TrackingNumberRecord("ABC123", "MY", "ID", 1.0, customerId),
            new TrackingNumberRecord("DEF456", "MY", "SG", 2.0, customerId)
        );
        when(trackingNumberService.getTrackingNumbersByCustomer(customerId)).thenReturn(mockRecords);

        // Act & Assert
        mockMvc.perform(get("/api/v1/tracking-numbers/customer/" + customerId))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].tracking_number").value("ABC123"))
                .andExpect(jsonPath("$[1].tracking_number").value("DEF456"));
    }

    @Test
    @WithMockUser
    void getTrackingNumbersByRoute_ShouldReturnList() throws Exception {
        // Arrange
        List<TrackingNumberRecord> mockRecords = Arrays.asList(
            new TrackingNumberRecord("ABC123", "MY", "ID", 1.0, "customer1"),
            new TrackingNumberRecord("DEF456", "MY", "ID", 2.0, "customer2")
        );
        when(trackingNumberService.getTrackingNumbersByRoute("MY", "ID")).thenReturn(mockRecords);

        // Act & Assert
        mockMvc.perform(get("/api/v1/tracking-numbers/route/MY/ID"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].origin_country_id").value("MY"))
                .andExpect(jsonPath("$[0].destination_country_id").value("ID"));
    }
}
