package org.example.controller;

import org.example.service.TrackingNumberService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;



import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = TrackingNumberController.class,
           excludeAutoConfiguration = {
               org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class,
               org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class
           })
class TrackingNumberControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TrackingNumberService trackingNumberService;

    @Test
    @WithMockUser
    void getNextTrackingNumber_ValidRequest_ShouldReturnTrackingNumber() throws Exception {
        // Arrange
        when(trackingNumberService.generateTrackingNumber(anyString(), anyString(), anyDouble(), anyString()))
            .thenReturn("ABC123DEF456GH78");

        // Act & Assert
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.tracking_number").value("ABC123DEF456GH78"))
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.origin_country_id").value("MY"))
                .andExpect(jsonPath("$.destination_country_id").value("ID"));
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidCountryCode_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "INVALID")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidWeight_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "-1.0")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidUUID_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "invalid-uuid")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void getNextTrackingNumber_InvalidCustomerSlug_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "Invalid_Slug"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getNextTrackingNumber_Unauthenticated_ShouldReturnUnauthorized() throws Exception {
        mockMvc.perform(get("/api/v1/next-tracking-number")
                .param("origin_country_id", "MY")
                .param("destination_country_id", "ID")
                .param("weight", "1.234")
                .param("customer_id", "de619854-b59b-425e-9db4-943979e1bd49")
                .param("customer_name", "RedBox Logistics")
                .param("customer_slug", "redbox-logistics"))
                .andExpect(status().isUnauthorized());
    }


}
