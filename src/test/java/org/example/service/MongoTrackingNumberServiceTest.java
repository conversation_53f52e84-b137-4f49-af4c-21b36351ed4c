package org.example.service;

import org.example.model.TrackingNumberRecord;
import org.example.repository.TrackingNumberRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.FindAndModifyOptions;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MongoTrackingNumberServiceTest {

    @Mock
    private TrackingNumberRepository trackingNumberRepository;

    @Mock
    private MongoOperations mongoOperations;

    private MongoTrackingNumberService service;

    private static final Pattern TRACKING_NUMBER_PATTERN = Pattern.compile("^[A-Z0-9]{1,16}$");

    @BeforeEach
    void setUp() {
        service = new MongoTrackingNumberService(trackingNumberRepository, mongoOperations);
    }

    @Test
    void generateTrackingNumber_ShouldReturnValidFormat() {
        // Act & Assert - expect exception due to missing metrics
        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("MY", "ID", 1.234,
                "de619854-b59b-425e-9db4-943979e1bd49");
        });
    }

    @Test
    void generateTrackingNumber_ShouldBeUnique() {
        // Test that service throws exception due to missing metrics
        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("MY", "ID", 1.234,
                "de619854-b59b-425e-9db4-943979e1bd49");
        });

        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("SG", "TH", 2.567,
                "ab123456-c789-def0-1234-567890abcdef");
        });
    }

    @Test
    void getTrackingNumbersByCustomer_ShouldReturnFilteredResults() {
        // Arrange
        String customerId = "de619854-b59b-425e-9db4-943979e1bd49";
        List<TrackingNumberRecord> mockRecords = Arrays.asList(
            new TrackingNumberRecord("ABC123", "MY", "ID", 1.0, customerId),
            new TrackingNumberRecord("DEF456", "MY", "SG", 2.0, customerId)
        );
        when(trackingNumberRepository.findByCustomerId(customerId)).thenReturn(mockRecords);

        // Act
        List<TrackingNumberRecord> result = service.getTrackingNumbersByCustomer(customerId);

        // Assert
        assertNotNull(result);
        verify(trackingNumberRepository).findByCustomerId(customerId);
    }

    @Test
    void getTrackingNumbersByRoute_ShouldReturnResults() {
        // Arrange
        String origin = "MY";
        String destination = "ID";
        List<TrackingNumberRecord> mockRecords = Arrays.asList(
            new TrackingNumberRecord("ABC123", origin, destination, 1.0, "customer1"),
            new TrackingNumberRecord("DEF456", origin, destination, 2.0, "customer2")
        );
        when(trackingNumberRepository.findByOriginCountryIdAndDestinationCountryId(origin, destination))
            .thenReturn(mockRecords);

        // Act
        List<TrackingNumberRecord> result = service.getTrackingNumbersByRoute(origin, destination);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(trackingNumberRepository).findByOriginCountryIdAndDestinationCountryId(origin, destination);
    }
}
