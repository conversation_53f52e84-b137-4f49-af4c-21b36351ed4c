package org.example.service;

import org.example.model.TrackingNumberRecord;
import org.example.repository.TrackingNumberRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.FindAndModifyOptions;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MongoTrackingNumberServiceTest {

    @Mock
    private TrackingNumberRepository trackingNumberRepository;

    @Mock
    private MongoOperations mongoOperations;

    private MongoTrackingNumberService service;

    private static final Pattern TRACKING_NUMBER_PATTERN = Pattern.compile("^[A-Z0-9]{1,16}$");

    @BeforeEach
    void setUp() {
        service = new MongoTrackingNumberService(trackingNumberRepository, mongoOperations);
    }

    @Test
    void generateTrackingNumber_ShouldReturnValidFormat() {
        // Act & Assert - expect exception due to missing metrics
        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("MY", "ID", 1.234,
                "de619854-b59b-425e-9db4-943979e1bd49");
        });
    }

    @Test
    void generateTrackingNumber_ShouldBeUnique() {
        // Test that service throws exception due to missing metrics
        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("MY", "ID", 1.234,
                "de619854-b59b-425e-9db4-943979e1bd49");
        });

        assertThrows(Exception.class, () -> {
            service.generateTrackingNumber("SG", "TH", 2.567,
                "ab123456-c789-def0-1234-567890abcdef");
        });
    }


}
