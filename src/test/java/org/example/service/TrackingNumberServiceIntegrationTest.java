package org.example.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = {
    org.example.TrackingNumberApplication.class,
    org.example.config.MetricsConfig.class
})
@TestPropertySource(properties = {
    "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration,org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration"
})
class TrackingNumberServiceIntegrationTest {

    private static final Pattern TRACKING_NUMBER_PATTERN = Pattern.compile("^[A-Z0-9]{1,16}$");

    @Test
    void contextLoads() {
        // Test that Spring context loads successfully
        assertTrue(true);
    }

    @Test
    void trackingNumberPattern_ShouldBeValid() {
        // Test the regex pattern itself
        assertTrue(TRACKING_NUMBER_PATTERN.matcher("ABC123DEF456GH78").matches());
        assertTrue(TRACKING_NUMBER_PATTERN.matcher("1234567890ABCDEF").matches());
        assertTrue(TRACKING_NUMBER_PATTERN.matcher("A").matches());
        assertTrue(TRACKING_NUMBER_PATTERN.matcher("1").matches());
        
        assertFalse(TRACKING_NUMBER_PATTERN.matcher("abc123").matches()); // lowercase
        assertFalse(TRACKING_NUMBER_PATTERN.matcher("ABC123DEF456GH789").matches()); // too long
        assertFalse(TRACKING_NUMBER_PATTERN.matcher("").matches()); // empty
        assertFalse(TRACKING_NUMBER_PATTERN.matcher("ABC-123").matches()); // special chars
    }
}
