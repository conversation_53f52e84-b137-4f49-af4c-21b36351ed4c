package org.example.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import static org.mockito.Mockito.mock;

@TestConfiguration
public class TestConfig {

    @Bean
    @Primary
    public Counter trackingNumberGeneratedCounter() {
        return mock(Counter.class);
    }

    @Bean
    @Primary
    public Timer trackingNumberGenerationTimer() {
        return mock(Timer.class);
    }

    @Bean
    @Primary
    public Counter trackingNumberErrorCounter() {
        return mock(Counter.class);
    }
}
